workflow:
  name: "Technical Documentation Generation"
  description: >
    <PERSON><PERSON><PERSON> ou met à jour la documentation technique d’un module, d’une fonctionnalité ou d’une API : analyse du code, des specs, rédaction structurée, gestion des ambiguïtés, traduction et enregistrement.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "define_scope"
      action: "Définir la cible et collecter le contexte"
      agent: "uber-orchestrator"
      inputs:
        target: "{{target}}"
        user_language: "{{user_language}}"
      outputs:
        context: "{{context}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "clarify_context"
      action: "Vérifier la clarté du contexte"
      agent: "uber-orchestrator"
      inputs:
        context: "{{context}}"
      condition: "is_ambiguous(context)"
      on_true:
        sub_workflow: "XX_Handle_Clarification_Response"
        wait_for_response: true
    - id: "analyze_source"
      action: "Analyser le code source et les specs"
      agent: "documentation-writer-agent"
      inputs:
        context: "{{context}}"
      outputs:
        analysis: "{{analysis}}"
    - id: "write_doc"
      action: "Ré<PERSON><PERSON> et traduire la documentation technique"
      agent: "documentation-writer-agent"
      inputs:
        analysis: "{{analysis}}"
        user_language: "{{user_language}}"
      outputs:
        doc_file: "{{doc_file}}"
    - id: "record_doc"
      action: "Enregistrer la documentation dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        doc_file: "{{doc_file}}"

  transitions: []
