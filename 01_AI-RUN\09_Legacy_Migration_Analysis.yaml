workflow:
  name: "Legacy Migration Analysis"
  description: >
    Analyse un code legacy pour migration vers .NET Core/Angular : ingestion, analyse détaillée, mapping, estimation de complexité et risques, génération et enregistrement du rapport.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "plan_analysis"
      action: "Planifier l’analyse et ingérer le code legacy"
      agent: "migration-analyst-agent"
      inputs:
        legacy_path: "{{legacy_path}}"
        target_stack: "{{target_stack}}"
      outputs:
        analysis_plan: "{{analysis_plan}}"
      on_fail:
        sub_workflow: "XX_Handle_Clarification_Response"
        wait_for_response: true
    - id: "analyze_components"
      action: "Analyser les composants legacy"
      agent: "migration-analyst-agent"
      inputs:
        analysis_plan: "{{analysis_plan}}"
      outputs:
        component_analysis: "{{component_analysis}}"
    - id: "map_modern_stack"
      action: "Proposer le mapping et les stratégies de migration"
      agent: "migration-analyst-agent"
      inputs:
        component_analysis: "{{component_analysis}}"
        target_stack: "{{target_stack}}"
      outputs:
        mapping: "{{mapping}}"
    - id: "estimate_complexity"
      action: "Estimer la complexité et les risques"
      agent: "migration-analyst-agent"
      inputs:
        mapping: "{{mapping}}"
      outputs:
        risks: "{{risks}}"
        complexity: "{{complexity}}"
    - id: "generate_report"
      action: "Générer et traduire le rapport d’analyse"
      agent: "migration-analyst-agent"
      inputs:
        analysis_plan: "{{analysis_plan}}"
        component_analysis: "{{component_analysis}}"
        mapping: "{{mapping}}"
        risks: "{{risks}}"
        complexity: "{{complexity}}"
        user_language: "{{user_language}}"
      outputs:
        report: "{{report}}"
    - id: "record_report"
      action: "Enregistrer le rapport dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"

  transitions: []
