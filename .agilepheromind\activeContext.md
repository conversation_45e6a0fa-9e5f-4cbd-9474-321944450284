# AgilePheromind: Active Work Context
# This file is managed by AgilePheromind.
# Location: .agilepheromind/activeContext.md
# This file IS VERSIONED by Git, reflecting the last known active items.

## Current Sprint Context
# Information about the sprint Pheromind considers active.
# This might be set during 'Sprint Planning Assistance' or a specific command.
# All textual descriptions that might be multi-lingual in ADO are stored here in English.

- **sprintId_ado:** `IterationPath\\Guid` # Azure DevOps Iteration Path or a specific ID if available
- **sprintName_en:** "Sprint Alpha - UI Finalization"
- **sprintGoal_en:** "Finalize the user interface for core features and prepare for UAT."
- **startDate:** `YYYY-MM-DD`
- **endDate:** `YYYY-MM-DD`
- **capacityPoints:** 0
- **userStory_ids_in_sprint:** # List of ADO User Story IDs included in this sprint according to Pheromind
    - `Azure#12345`
    - `Azure#12348`
- **status_en:** `InProgress` # e.g., "Planning", "InProgress", "Review", "Completed" (<PERSON><PERSON><PERSON>'s view of the sprint)

## Active User Story Context
# Details of the User Story currently being actively processed by a Pheromind workflow.
# Set when a workflow like 'Start User Story' or 'Continue Task' is initiated.
# All textual descriptions are stored here in English.

- **id_ado:** `Azure#12345`
- **title_en:** "User Login with Two-Factor Authentication"
- **status_pheromind_en:** `InProgressByPheromind` # Pheromind's internal status for the US
                                                 # e.g., "New", "ClarificationPending", "DecompositionInProgress",
                                                 # "DevelopmentInProgress", "ReadyForReviewByPheromind", "CompletedByPheromind"
- **linkToMemoryBankEntry:** `memoryBank/userStories/US_12345.md`

### Associated Tasks (Brief Pheromind View)
# A quick reference to tasks for the active US. Full details are in their respective memoryBank/tasks/TASK_ID.md files.
# This list is primarily for the UO to quickly see task status for the active US.
# - id_ado: "Azure#67890"
#   title_en: "Implement backend API endpoint for 2FA validation"
#   status_pheromind_en: "ToDo"
#   assignee_pheromind_id_en: "a1b2c3d4-e5f6-7890-1234-567890abcdef" # Pheromind User ID

- id_ado: `Azure#67890`
  title_en: "Implement backend API endpoint for 2FA validation"
  status_pheromind_en: `InProgress`
  assignee_pheromind_id_en: `a1b2c3d4-e5f6-7890-1234-567890abcdef`
- id_ado: `Azure#67891`
  title_en: "Develop frontend UI for 2FA code input"
  status_pheromind_en: `ToDo`
  assignee_pheromind_id_en: null


## Active Task Context
# Details of the specific technical task currently being actively processed.
# Set when a workflow like 'Continue Task' or a sub-phase of 'Start User Story' is active.
# All textual descriptions are stored here in English.

- **id_ado:** `Azure#67890`
- **title_en:** "Implement backend API endpoint for 2FA validation"
- **status_pheromind_en:** `DevelopmentInProgress` # Pheromind's internal status for the task
                                                # e.g., "ToDo", "DevelopmentInProgress", "Blocked",
                                                # "ReadyForCommit", "Committed", "DoneInPheromind"
- **parent_us_id_ado:** `Azure#12345`
- **linkToMemoryBankEntry:** `memoryBank/tasks/TASK_67890.md`

---