workflow:
  name: "Generate Unit Test Skeletons"
  description: >
    Génère des squelettes de tests unitaires pour une méthode ou composant cible, en s’appuyant sur le code source, les spécifications et les conventions de test.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "fetch_code"
      action: "Récupérer le code source cible"
      agent: "devops-connector"
      inputs:
        target: "{{target}}"
      outputs:
        code: "{{code}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "inject_context"
      action: "Injecter le contexte fonctionnel et les conventions de test"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        code: "{{code}}"
      outputs:
        context: "{{context}}"
    - id: "clarify_target"
      action: "Vérifier la clarté du code et des specs"
      agent: "uber-orchestrator"
      inputs:
        context: "{{context}}"
      condition: "is_ambiguous(context)"
      on_true:
        sub_workflow: "XX_Handle_Clarification_Response"
        wait_for_response: true
    - id: "generate_tests"
      action: "Générer les squelettes de tests et le rapport de scénarios"
      agent: "test-generator-agent"
      inputs:
        context: "{{context}}"
      outputs:
        test_files: "{{test_files}}"
        scenario_report: "{{scenario_report}}"
      on_fail:
        sub_workflow: "XX_Handle_Clarification_Response"
    - id: "record_results"
      action: "Enregistrer les résultats et le rapport"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        test_files: "{{test_files}}"
        scenario_report: "{{scenario_report}}"

  transitions: []
