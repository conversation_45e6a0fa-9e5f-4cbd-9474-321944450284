workflow:
  name: "Sprint Planning Assistance"
  description: >
    Assiste le PO et l’équipe pour planifier un sprint : récupération des US candidates, estimation, planification selon la capacité, analyse des risques, génération du plan et gestion des clarifications.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "fetch_us"
      action: "Récupérer les détails des User Stories candidates"
      agent: "devops-connector"
      inputs:
        us_ids: "{{us_ids}}"
      outputs:
        us_details: "{{us_details}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "clarify_us"
      action: "Vérifier la clarté des US candidates"
      agent: "uber-orchestrator"
      inputs:
        us_details: "{{us_details}}"
      condition: "is_ambiguous(us_details)"
      on_true:
        sub_workflow: "XX_Handle_Clarification_Response"
        wait_for_response: true
    - id: "estimate_tasks"
      action: "Estimer et découper les US candidates"
      agent: "task-breakdown-estimator"
      inputs:
        us_details: "{{us_details}}"
      outputs:
        estimates: "{{estimates}}"
      on_fail:
        notify_user: true
    - id: "plan_sprint"
      action: "Proposer un plan de sprint selon la capacité et les priorités"
      agent: "scrum-facilitator-agent"
      inputs:
        estimates: "{{estimates}}"
        capacity: "{{capacity}}"
      outputs:
        sprint_plan: "{{sprint_plan}}"
    - id: "compile_report"
      action: "Compiler et traduire le rapport de planification"
      agent: "scrum-facilitator-agent"
      inputs:
        sprint_plan: "{{sprint_plan}}"
        user_language: "{{user_language}}"
      outputs:
        report: "{{report}}"
    - id: "record_plan"
      action: "Enregistrer le plan et le rapport"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"
        sprint_plan: "{{sprint_plan}}"

  transitions: []
