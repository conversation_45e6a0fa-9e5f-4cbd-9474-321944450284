workflow:
  name: "Handle User Need"
  description: >
    Traite un besoin utilisateur en séquençant les agents RooCode existants : analyse du besoin, découpage en US/tâches, développement.

  steps:
    - id: "analyze_need"
      action: "Analyser le besoin utilisateur"
      agent: "swarm-agent"
      inputs:
        agent_name: "po-assistant"
        need: "{{need}}"
      outputs:
        us: "{{us}}"
    - id: "breakdown_us"
      action: "Découper la User Story en tâches"
      agent: "task-breakdown-estimator"
      inputs:
        us: "{{us}}"
      outputs:
        tasks: "{{tasks}}"
    - id: "develop_task"
      action: "Développer la première tâche"
      agent: "developer-agent"
      inputs:
        tasks: "{{tasks}}"
      outputs:
        dev_result: "{{dev_result}}"

  transitions: []
