# AgilePheromind: Current User and Project Configuration
# This file is managed by AgilePheromind.
# Location: .agilepheromind/currentUser_and_Project.md
# IMPORTANT: This file SHOULD BE INCLUDED IN .gitignore

## Current User

### Identity
- **pheromindId:** `a1b2c3d4-e5f6-7890-1234-567890abcdef`
- **azureDevOpsUsername:** `<EMAIL>`
- **azureDevOpsDisplayName:** `KRISTOU.OMNILOG, Abdessalam`
- **azureDevOpsUserId:** `23c28fd1-632a-4a92-ab39-f5f08cc4c4ad`

### Preferences
- **preferredLanguage:** `fr`
- **lastInteractionLanguage:** `fr` # Updated by UO at the start of each interaction

### Session & Context (Potentially for future enhancements)
- **roles:** [] # e.g., ["Developer", "PO"] - could be auto-detected or set
- **currentGeneralContext_en:** null # e.g., "Focusing on API security for payment module"
- **activeSessionId:** null

## Current Project Connection

### Pheromind Project ID
- **pheromindProjectId:** `f9e8d7c6-b5a4-3210-fedc-ba9876543210` # UUID for this project within Pheromind context

### Azure DevOps Project Details
- **organizationUrl:** `https://dev.azure.com/groupe-tf1`
- **projectName:** `PPP-Applicatif`
- **projectId:** `2e247dff-b815-46c3-b79f-bf8b4918c96b`

### Local Git Repository
- **localPath:** `C:\\Users\\<USER>\\source\\repos\\agile-pheromind` # Full absolute path
- **remoteUrl:** `https://dev.azure.com/groupe-tf1/PPP-Applicatif/_git/PPP-Applicatif` # Typically auto-detected or confirmed
- **defaultBranchPheromind:** `develop` # Default branch for Pheromind operations, might differ from repo default

### Project Nickname (Optional, for I.1. Project Switching)
- **projectNickname:** `ppp-app` # User-defined short name for easy switching, e.g., "AgilePheromind switch to ppp-app"

---