workflow:
  name: "UI Test Validation"
  description: >
    Valide l’interface utilisateur d’une fonctionnalité ou US : récupération des specs, exécution des scénarios UI, comparaison, génération et enregistrement du rapport.

  steps:
    - id: "fetch_specs"
      action: "Récupérer les spécifications et AC"
      agent: "devops-connector"
      inputs:
        us_id: "{{us_id}}"
      outputs:
        specs: "{{specs}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "run_ui_tests"
      action: "Exécuter les scénarios UI sur l’environnement de test"
      agent: "tester-ui-validator-agent"
      inputs:
        specs: "{{specs}}"
        test_url: "{{test_url}}"
      outputs:
        test_results: "{{test_results}}"
      on_fail:
        notify_user: true
    - id: "compare_results"
      action: "Comparer les résultats aux specs"
      agent: "tester-ui-validator-agent"
      inputs:
        test_results: "{{test_results}}"
        specs: "{{specs}}"
      outputs:
        validation: "{{validation}}"
    - id: "generate_report"
      action: "Générer et traduire le rapport de validation UI"
      agent: "tester-ui-validator-agent"
      inputs:
        validation: "{{validation}}"
        user_language: "{{user_language}}"
      outputs:
        report: "{{report}}"
    - id: "record_report"
      action: "Enregistrer le rapport dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"

  transitions: []
