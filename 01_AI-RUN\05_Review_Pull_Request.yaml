workflow:
  name: "Review Pull Request"
  description: >
    Assiste le Tech Lead dans la revue d’une Pull Request Azure DevOps : récupération des infos, analyse qualité/sécurité, génération d’un rapport détaillé, gestion des erreurs et clarification.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "fetch_pr"
      action: "Récupérer les détails de la Pull Request"
      agent: "devops-connector"
      inputs:
        pr_id: "{{pr_id}}"
      outputs:
        pr_details: "{{pr_details}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "setup_review_dir"
      action: "Préparer le dossier de revue"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        pr_details: "{{pr_details}}"
      outputs:
        review_dir: "{{review_dir}}"
    - id: "fetch_diffs"
      action: "Récupérer les changements de code"
      agent: "code-reviewer-assistant"
      inputs:
        pr_details: "{{pr_details}}"
      outputs:
        diffs: "{{diffs}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "analyze_code"
      action: "Analyser les changements (qualité, sécurité, conventions)"
      agent: "code-reviewer-assistant"
      inputs:
        diffs: "{{diffs}}"
      outputs:
        analysis: "{{analysis}}"
      on_ambiguous:
        sub_workflow: "XX_Handle_Clarification_Response"
        wait_for_response: true
    - id: "compile_report"
      action: "Compiler et traduire le rapport de revue"
      agent: "code-reviewer-assistant"
      inputs:
        analysis: "{{analysis}}"
        review_dir: "{{review_dir}}"
        user_language: "{{user_language}}"
      outputs:
        report: "{{report}}"
    - id: "record_report"
      action: "Enregistrer le rapport dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"
        pr_details: "{{pr_details}}"

  transitions: []
