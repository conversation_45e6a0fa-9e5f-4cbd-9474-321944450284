workflow:
  name: "Proactive Risk Management"
  description: >
    <PERSON><PERSON><PERSON><PERSON>, évalue et enregistre les risques projet : collecte des données, mise à jour du registre, génération et enregistrement du rapport.

  steps:
    - id: "collect_risks"
      action: "Collecter les risques depuis la base et Azure DevOps"
      agent: "devops-connector"
      outputs:
        risks: "{{risks}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "update_risk_register"
      action: "Mettre à jour le registre des risques"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        risks: "{{risks}}"
    - id: "generate_report"
      action: "Générer et traduire le rapport de risques"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        risks: "{{risks}}"
        user_language: "{{user_language}}"
      outputs:
        report: "{{report}}"
    - id: "record_report"
      action: "Enregistrer le rapport dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"

  transitions: []
