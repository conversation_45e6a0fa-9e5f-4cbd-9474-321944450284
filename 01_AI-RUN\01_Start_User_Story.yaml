workflow:
  name: "Start User Story"
  description: >
    Initialise le travail sur une User Story Azure DevOps : vérification onboarding, récupération US, clarification, découpage technique, synchronisation ADO, préparation du premier task.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "fetch_us"
      action: "Récupérer les détails de la User Story"
      agent: "devops-connector"
      inputs:
        us_id: "{{us_id}}"
      outputs:
        us_details: "{{us_details}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "update_context"
      action: "Mettre à jour le contexte interne et traduire en anglais"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        us_details: "{{us_details}}"
      outputs:
        us_en: "{{us_en}}"
    - id: "clarify_us"
      action: "Valider la clarté de la User Story"
      agent: "uber-orchestrator"
      inputs:
        us_en: "{{us_en}}"
      condition: "is_ambiguous(us_en)"
      on_true:
        sub_workflow: "XX_Handle_Clarification_Response"
        wait_for_response: true
    - id: "breakdown"
      action: "Découper la User Story en tâches techniques et estimer"
      agent: "task-breakdown-estimator"
      inputs:
        us_en: "{{us_en}}"
      outputs:
        tasks: "{{tasks}}"
      on_fail:
        sub_workflow: "XX_Handle_Clarification_Response"
    - id: "sync_tasks"
      action: "Synchroniser les tâches avec Azure DevOps"
      agent: "devops-connector"
      inputs:
        tasks: "{{tasks}}"
    - id: "prepare_first_task"
      action: "Préparer le premier task pour le développement"
      agent: "developer-agent"
      inputs:
        tasks: "{{tasks}}"
      outputs:
        branch: "{{branch}}"
      on_fail:
        notify_user: true

  transitions: []
