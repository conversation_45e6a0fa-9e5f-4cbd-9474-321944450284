workflow:
  name: "Manage Docker Images"
  description: >
    Build et push des images Docker pour les applications du projet : récupération du Dockerfile, build, push vers ACR, génération et enregistrement du rapport.

  steps:
    - id: "fetch_dockerfile"
      action: "R<PERSON><PERSON><PERSON>rer le Dockerfile cible"
      agent: "deployment-agent-aks"
      inputs:
        app_name: "{{app_name}}"
        version: "{{version}}"
      outputs:
        dockerfile: "{{dockerfile}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "build_image"
      action: "Construire l’image Docker"
      agent: "deployment-agent-aks"
      inputs:
        dockerfile: "{{dockerfile}}"
        app_name: "{{app_name}}"
        version: "{{version}}"
      outputs:
        build_status: "{{build_status}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "push_image"
      action: "Pousser l’image sur Azure Container Registry"
      agent: "deployment-agent-aks"
      inputs:
        app_name: "{{app_name}}"
        version: "{{version}}"
      outputs:
        push_status: "{{push_status}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "generate_report"
      action: "Générer et traduire le rapport de build/push"
      agent: "deployment-agent-aks"
      inputs:
        build_status: "{{build_status}}"
        push_status: "{{push_status}}"
        user_language: "{{user_language}}"
      outputs:
        report: "{{report}}"
    - id: "record_report"
      action: "Enregistrer le rapport dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"

  transitions: []
