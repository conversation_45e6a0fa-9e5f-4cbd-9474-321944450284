workflow:
  name: "System Bootstrap"
  description: >
    Guide l’utilisateur dans la configuration initiale d’AgilePheromind : collecte des infos ADO, langue, projet, repo Git, initialisation des fichiers et conventions.

  steps:
    - id: "welcome_language"
      action: "Confirmer ou collecter la langue préférée"
      agent: "uber-orchestrator"
      inputs:
        user_language: "{{user_language}}"
      outputs:
        preferred_language: "{{preferred_language}}"
    - id: "collect_ado_user"
      action: "Collecter et valider l’identité Azure DevOps"
      agent: "devops-connector"
      inputs:
        preferred_language: "{{preferred_language}}"
      outputs:
        ado_user: "{{ado_user}}"
      on_fail:
        notify_user: true
        retry: true
    - id: "collect_project_info"
      action: "Collecter et valider les infos projet ADO et Git"
      agent: "devops-connector"
      inputs:
        ado_user: "{{ado_user}}"
        preferred_language: "{{preferred_language}}"
      outputs:
        project_info: "{{project_info}}"
      on_fail:
        notify_user: true
        retry: true
    - id: "consolidate_info"
      action: "Structurer les infos pour enregistrement"
      agent: "project-setup-agent"
      inputs:
        ado_user: "{{ado_user}}"
        project_info: "{{project_info}}"
        preferred_language: "{{preferred_language}}"
      outputs:
        structured_data: "{{structured_data}}"
    - id: "record_info"
      action: "Enregistrer les infos utilisateur et projet"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        structured_data: "{{structured_data}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "init_conventions"
      action: "Initialiser les conventions si besoin"
      agent: "architecture-advisor-agent"
      inputs:
        project_info: "{{project_info}}"
      outputs:
        conventions: "{{conventions}}"
    - id: "final_report"
      action: "Informer l’utilisateur de la réussite et enregistrer le rapport"
      agent: "uber-orchestrator"
      inputs:
        conventions: "{{conventions}}"
        structured_data: "{{structured_data}}"

  transitions: []
