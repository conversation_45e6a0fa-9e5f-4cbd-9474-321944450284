workflow:
  name: "Test Workflow"
  description: "A simple workflow to test the agent swarm."
  steps:
    - id: "get_config"
      agent: "swarm-agent"
      inputs:
        agent_name: "po-assistant"
      outputs:
        config: "{{config}}"
    - id: "print_config"
      agent: "developer-agent"
      inputs:
        config: "{{config}}"
      outputs:
        result: "{{result}}"
  transitions:
    - from: "get_config"
      to: "print_config"