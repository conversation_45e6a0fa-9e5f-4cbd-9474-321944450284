# MemoryBank: Project Context
# Location: .agilepheromind/memoryBank/projectContext.md

## Technology Stack (English)
- .NET Core
- C#
- Angular
- TypeScript
# ... (liste complète)

## Conventions (Links to English Documents)
- **Coding Conventions Version:** `1.1_updated`
- **Path to Coding Conventions:** `02_AI-DOCS/Conventions/coding_conventions.md`
- **Design Conventions Version:** `1.0_initial`
- **Path to Design Conventions:** `02_AI-DOCS/Conventions/design_conventions.md`

## Project Management & Process (English)
- **Default Git Branching Strategy:** "GitFlow-like (feature -> develop -> main)"
- **Estimation Unit:** "points"
- **Risk Matrix Definition Link:** `02_AI-DOCS/Project_Management/risk_matrix.md` # (If defined)

## Azure DevOps Project (Mirror for quick access, master in currentUser_and_Project.md)
- **Organization URL:** `https://dev.azure.com/groupe-tf1`
- **Project Name:** `PPP-Applicatif`
- **Project ID:** `2e247dff-b815-46c3-b79f-bf8b4918c96b`

## Local Git Repository (Mirror for quick access, master in currentUser_and_Project.md)
- **Primary Local Path (from User Config):** `C:\\Users\\<USER>\\source\\repos\\agile-pheromind`

## Key Tooling Configuration Paths (English)
# Pointers to where specific tool configurations might be found in the project repo
- **.NET EditorConfig:** `.editorconfig`
- **Angular ESLintConfig:** `.eslintrc.json`
- **Angular PrettierConfig:** `.prettierrc.json`
- **Backend Dockerfile:** `src/ProjectName.Api/Dockerfile` (Example)
- **Frontend Dockerfile:** `src/ProjectName.WebApp/Dockerfile` (Example)
- **Main Azure Pipeline:** `.azuredevops/main-ci-cd.yml` (Example)

## Azure Resources (English)
- **Azure Container Registry URL:** `youracrname.azurecr.io`
- **AKS Deployment Pipeline ID (ADO):** `123` (ID of the ADO pipeline)

## Default Paths for Pheromind Artifacts (Relative to Project Root)
- **AI-RUN Scripts:** `01_AI-RUN/`
- **AI-DOCS (Generated & Templates):** `02_AI-DOCS/`
- **SPECS & Logs:** `03_SPECS/`
- **PR Reviews:** `04_PR_REVIEWS/`