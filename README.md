# AgilePheromind: AI-Powered Agile Development Assistant

[![License: MIT](https://img.shields.io/badge/License-MIT-blue.svg)](https://opensource.org/licenses/MIT)
[![Build Status](https://img.shields.io/badge/build-alpha-orange.svg)]()
[![Contributions Welcome](https://img.shields.io/badge/contributions-welcome-brightgreen.svg?style=flat)]()

**Transform your Agile development lifecycle with AgilePheromind, an intelligent AI swarm designed to assist .NET/Angular teams using Azure DevOps.**

AgilePheromind is an advanced AI-powered multi-agent system built to enhance and streamline Agile software development processes. It leverages a sophisticated architecture of specialized AI agents that collaborate to automate, assist, and optimize tasks from requirements analysis through to deployment and maintenance.

**Inspired by the innovative [Pheromind Framework by <PERSON>](https://github.com/ChrisRoyse/Pheromind)**, AgilePheromind adapts its principles of swarm intelligence, decentralized state management, and workflow orchestration to specifically address the needs of development teams working within the .NET, Angular, and Azure DevOps ecosystems.

## 🌟 Overview: What is AgilePheromind?

AgilePheromind acts as an intelligent co-pilot for your Agile team. It's not just a collection of scripts, but a dynamic system where AI agents, each with a specific role (like PO Assistant, Developer Agent, Code Reviewer, Migration Analyst), work together. They interact by reading and modifying a shared, structured environment of Markdown files managed by the `✍️ @orchestrator-pheromone-scribe`, enabling complex, context-aware assistance.

**Key Goals:**
*   Boost Productivity: Automate repetitive tasks, accelerate analysis, and provide rapid contextual support.
*   Enhance Quality: Integrate automated code analysis, test generation, and adherence to project conventions.
*   Improve Collaboration: Centralize project knowledge and support Agile ceremonies.
*   Facilitate Continuous Learning: Build a persistent `memoryBank` (a collection of Markdown files) of project decisions, solutions, and learnings, **including user feedback on agent performance.**
*   Seamless Tool Integration: Natively interact with Azure DevOps, Git, MSSQL, and other essential development tools via Model Context Protocol (MCP) servers.

## 🤔 Why Use AgilePheromind?

This framework is built to solve key challenges in modern Agile development:

*   Contextual AI Assistance: Agents understand your project's specifics (tech stack, conventions, history) stored in the `memoryBank`.
*   Full Lifecycle Support: From analyzing Product Owner needs to assisting with PR reviews and legacy code migration.
*   **Enhanced User Interaction:**
    *   Communicate with AgilePheromind in your preferred language; it handles internal processing and data storage consistently in English.
    *   **Contextual help system** to discover available workflows and their usage.
    *   **Interactive feedback mechanism** allowing users to rate and comment on agent-generated artifacts, fostering continuous improvement.
*   Robust Onboarding: A guided setup process ensures AgilePheromind is correctly configured for your existing Azure DevOps project and local Git repository.
*   Resilient Workflows: Built-in error handling and clarification loops make the system more robust.
*   Adaptable & Evolutive: Designed to be customized and to learn from project interactions.
*   **Optimized for LLM Context:** The state and memory are now distributed across multiple Markdown files, allowing the system to load only relevant information for each task, significantly reducing token consumption.

## ✨ Core Features

*   🧠 **Pheromone-Based Swarm Intelligence:** Decentralized agent coordination via a shared environment of Markdown files, managed by the `✍️ @orchestrator-pheromone-scribe`.
*   🎯 **Workflow-Driven Execution:** Project operations are defined by clear, modifiable scripts in the `01_AI-RUN/` directory.
*   🛠️ **Specialized AI Agents (`.roomodes`):** A team of virtual experts (PO Assistant, .NET/Angular Developer, Test Generator, Code Reviewer, Migration Analyst, Risk Manager, etc.).
*   🔗 **Deep MCP Integration:** Native interaction with Azure DevOps (Work Items, PRs, Pipelines), Git, Context7 (documentation), MSSQL, Browser Tools, and more.
*   🌍 **Multilingual User Interface:** Interact in your language; the system manages internal data in English.
*   📚 **Rich `memoryBank` (Distributed Markdown Files):** Stores project history, decisions, conventions, analyses, risks, learnings, and user feedback. All core data is in English.
*   ⚙️ **Automated Onboarding:** Configures itself for your existing Azure DevOps project and local Git setup.
*   💡 **"Chain of Thought" Logging:** Analytical agents document their reasoning for transparency and learning.
*   🛡️ **Integrated Error Handling & Clarification Loops:** For more robust and user-friendly operation.
*   **User-Friendly Help System:** Enables discovery of workflows and commands via `@head-orchestrator`.
*   **Feedback Loop:** Collects user feedback on agent outputs to guide system improvement.

## ⚙️ How It Works: The AgilePheromind Flow

1.  **User Onboarding (`01_AI-RUN/00_System_Bootstrap.md`):**
    *   On first interaction (or if critical info is missing), AgilePheromind guides you to provide your Azure DevOps identity, preferred language, and details about your ADO project and local Git repository. This information is stored locally in `.agilepheromind/currentUser_and_Project.md` (gitignored) and the initial structure of other state files is created.
2.  **User Command:** You issue a command in your language to the `🎩 @head-orchestrator` (e.g., `"AgilePheromind start US Azure#12323"` or `"AgilePheromind help"`).
3.  **Workflow Initiation:** The `🎩 @head-orchestrator` detects your language.
    *   If the command is for help, HO triggers a help workflow via UO to list relevant commands from `memoryBank/availableWorkflows.md`.
    *   Otherwise, HO selects the appropriate `01_AI-RUN/*.md` script and tasks the `🧐 @uber-orchestrator` (UO).
4.  **Orchestration & Context:** The UO reads the script and **selectively loads relevant Markdown files** from the Pheromind state and `memoryBank` (English state/MemoryBank). It injects targeted English context into specialized agents.
5.  **Agent Execution:** Specialized agents perform their tasks, operating with English inputs and producing English internal results/summaries.
6.  **User Interaction & Document Localization:**
    *   Clarification loops and final document localization proceed as before.
    *   **Feedback Collection:** For certain workflows, the UO may prompt the user (via HO, in user's language) for feedback on the generated artifacts. This feedback is stored (in English) in `memoryBank/feedbackOnAgentActions.md`.
7.  **State Update:** Agents send English Natural Language (NL) summaries to the `✍️ @orchestrator-pheromone-scribe`. The Scribe uses `.swarmConfig` to interpret these and **updates the appropriate Markdown files** in the Pheromind state and `memoryBank`.
8.  **Cycle Continuation:** The UO proceeds to the next phase or concludes the workflow.

For a conceptual overview with diagrams, see [`ARCHITECTURE.md`](./ARCHITECTURE.md) (assuming you'll create this based on my previous output).

## 🚀 Getting Started

1.  Clone this Repository.
2.  Prerequisites: (Same as before)
3.  Initial Configuration:
    *   Review and potentially customize `.roomodes` and `.swarmConfig`.
    *   **No manual creation of Pheromind state files is needed.**
4.  First Interaction (Onboarding):
    *   Issue your first command to AgilePheromind via your LLM interface, targeting the `🎩 @head-orchestrator`.
    *   The `🧐 @uber-orchestrator` will trigger `01_AI-RUN/00_System_Bootstrap.md`.
    *   Follow prompts. This will create the `.agilepheromind/` directory structure.
5.  Start Using Workflows. You can now also use:
    *   `@head-orchestrator AgilePheromind help`
    *   `@head-orchestrator AgilePheromind help with [topic]`

## 📚 Key System Files and Structure

*   **`.agilepheromind/` (Directory, Base for Pheromind State and Memory)**
    *   **`currentUser_and_Project.md` (Local, Gitignored):** User-specific identity, active project connection details, language preference. Created/updated during onboarding and project switching.
    *   **`systemState.md` (Versioned):** Core system status, active workflow details, documentation registry, list of available workflows, etc.
    *   **`activeContext.md` (Versioned, but frequently changing):** Details of the current sprint, active User Story, and active Task.
    *   **`memoryBank/` (Directory, Versioned):**
        *   Contains various `.md` files and subdirectories storing the project's knowledge base (e.g., `projectContext.md`, `userProfiles.md`, `userStories/US_ID.md`, `tasks/TASK_ID.md`, `feedbackOnAgentActions.md`, etc.). All data is in English.
        *   Refer to `CONVENTIONS_MEMORYBANK.md` (to be created) for details on the structure of these files.
*   **`.roomodes`:** (Same as before)
*   **`.swarmConfig`:** (Same as before, but logic will target specific `.md` files)
*   **`01_AI-RUN/`:** (Same as before)
*   **`02_AI-DOCS/`, `03_SPECS/`, `04_PR_REVIEWS/`:** (Same as before, store generated documents, logs, and reports. `documentationRegistry` in `systemState.md` will link to these).

## 🛠️ Technology Stack Focus

(Same as before)

## 🤝 Contributing / Future Vision

AgilePheromind is an ambitious project with vast potential. We envision it evolving into an even more deeply integrated and proactive AI partner for Agile teams. Contributions, ideas, and feedback are highly welcome!

**Future Directions:**
*   More sophisticated learning capabilities for agents based on `memoryBank` analysis, **including direct user feedback**.
*   Richer MCP integrations.
*   Visual dashboard for Pheromind state and `memoryBank` insights.
*   Enhanced natural language understanding for user commands.
*   More proactive suggestions and risk alerts.
*   **Formalized schema and validation for `memoryBank` Markdown files.**

---