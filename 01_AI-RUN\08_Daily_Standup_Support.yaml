workflow:
  name: "Daily Stand-up Support"
  description: >
    Génère un résumé structuré de l’avancement du sprint pour le Daily : collecte des données ADO et mémoire, analyse des progrès et blocages, génération et enregistrement du rapport, notification à l’équipe.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "fetch_sprint_data"
      action: "Récupérer les données du sprint actif depuis Azure DevOps"
      agent: "devops-connector"
      inputs:
        sprint_id: "{{sprint_id}}"
      outputs:
        sprint_data: "{{sprint_data}}"
      on_fail:
        notify_user: true
    - id: "update_memory"
      action: "Mettre à jour la mémoire avec les données ADO"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        sprint_data: "{{sprint_data}}"
    - id: "analyze_progress"
      action: "Analyser les progrès, points d’attention et blocages"
      agent: "scrum-facilitator-agent"
      inputs:
        sprint_data: "{{sprint_data}}"
      outputs:
        analysis: "{{analysis}}"
    - id: "generate_report"
      action: "Générer le rapport structuré du Daily"
      agent: "scrum-facilitator-agent"
      inputs:
        analysis: "{{analysis}}"
      outputs:
        report: "{{report}}"
    - id: "record_report"
      action: "Enregistrer le rapport dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"
    - id: "notify_team"
      action: "Notifier l’équipe (si déclenchement manuel)"
      agent: "uber-orchestrator"
      inputs:
        report: "{{report}}"
        user_language: "{{user_language}}"

  transitions: []
