workflow:
  name: "PO Need Analysis"
  description: >
    Analyse un besoin client exprimé en langage naturel, propose des User Stories et AC, vérifie les doublons, gère les ambiguïtés et fournit un rapport final localisé.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "analyze_need"
      action: "Analyser le besoin client et structurer l'analyse"
      agent: "po-assistant"
      inputs:
        need: "{{need}}"
        user_language: "{{user_language}}"
      outputs:
        analysis: "{{analysis}}"
      on_fail:
        notify_user: true
        stop: true
      on_ambiguous:
        sub_workflow: "XX_Handle_Clarification_Response"
        wait_for_response: true
    - id: "generate_us"
      action: "Générer les User Stories et AC"
      agent: "po-assistant"
      inputs:
        analysis: "{{analysis}}"
      outputs:
        user_stories: "{{user_stories}}"
    - id: "search_duplicates"
      action: "Vérifier les doublons dans le backlog"
      agent: "devops-connector"
      inputs:
        user_stories: "{{user_stories}}"
      outputs:
        existing_us: "{{existing_us}}"
      on_fail:
        notify_user: true
    - id: "compile_report"
      action: "Compiler et traduire le rapport d'analyse"
      agent: "po-assistant"
      inputs:
        analysis: "{{analysis}}"
        user_stories: "{{user_stories}}"
        existing_us: "{{existing_us}}"
        user_language: "{{user_language}}"
      outputs:
        report: "{{report}}"
    - id: "record_report"
      action: "Enregistrer le rapport et les US"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"
        user_stories: "{{user_stories}}"

  transitions: []
