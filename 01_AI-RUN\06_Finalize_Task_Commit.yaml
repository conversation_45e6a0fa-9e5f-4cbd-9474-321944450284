workflow:
  name: "Finalize Task and Commit"
  description: >
    Guide le développeur pour finaliser une tâche : vérification pré-commit, génération du message, exécution du commit, mise à jour des statuts et gestion des erreurs.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "precommit_check"
      action: "Vérifier que les tests et linters passent"
      agent: "developer-agent"
      inputs:
        task_id: "{{task_id}}"
      outputs:
        check_status: "{{check_status}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "generate_commit_msg"
      action: "Générer le message de commit Conventional Commits"
      agent: "commit-pr-formatter"
      inputs:
        task_id: "{{task_id}}"
      outputs:
        commit_msg: "{{commit_msg}}"
    - id: "validate_commit_msg"
      action: "Valider le message de commit avec l'utilisateur"
      agent: "uber-orchestrator"
      inputs:
        commit_msg: "{{commit_msg}}"
        user_language: "{{user_language}}"
      outputs:
        validated_msg: "{{validated_msg}}"
    - id: "execute_commit"
      action: "Exécuter le commit avec le message validé"
      agent: "developer-agent"
      inputs:
        validated_msg: "{{validated_msg}}"
      outputs:
        commit_hash: "{{commit_hash}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "update_status"
      action: "Mettre à jour le statut dans Azure DevOps"
      agent: "devops-connector"
      inputs:
        task_id: "{{task_id}}"
        commit_hash: "{{commit_hash}}"
      outputs:
        ado_status: "{{ado_status}}"
      on_fail:
        notify_user: true
    - id: "record_commit"
      action: "Enregistrer le commit et l’état dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        task_id: "{{task_id}}"
        commit_hash: "{{commit_hash}}"
        ado_status: "{{ado_status}}"

  transitions: []
