{"scribeSettings": {"defaultPheromoneFile": ".pheromone", "backupPheromoneBeforeWrite": true, "logLevel": "INFO", "defaultLanguageForMemoryBank": "en-US"}, "interpretationLogic": [{"id": "handle_configuration_request", "priority": 10, "conditions": [{"field": "<PERSON><PERSON><PERSON>", "operator": "notEquals", "value": "🐝 @swarm-agent"}, {"field": "summaryText_en", "operator": "contains", "value": "Request Configuration from Swarm Agent"}], "actions": [{"type": "sendMessageToSwarmAgent", "message": {"agent": "{{<PERSON><PERSON><PERSON>}}", "request": "configuration"}}]}, {"id": "handle_swarm_agent_response", "priority": 11, "conditions": [{"field": "<PERSON><PERSON><PERSON>", "operator": "equals", "value": "🐝 @swarm-agent"}, {"field": "summaryText_en", "operator": "contains", "value": "Configuration for agent"}], "actions": [{"type": "updateAgentConfiguration", "agent": "{{summaryText_en | extractAgentName}}", "configuration": "{{structuredData.configuration}}"}]}], "actionImplementations": {"sendMessageToSwarmAgent": "Sends a message to the Swarm Agent.", "updateAgentConfiguration": "Updates the configuration of an agent."}, "valueExtractors": {"extractFilename": "Extracts filename from path.", "extractAgentName": "Extracts the agent name from the summary text.", "extractFilenameWithoutExt": "Extracts filename without extension.", "extractKeyFindings": "Extracts short English summary of key findings.", "extractOverallStatus": "Determines status (OK, Warning, Error, Pass, Fail) from English summary.", "extractLogLink": "Finds log file URLs/paths in English summary.", "extractLogPath": "Extracts file path for logging from English summary.", "toInt": "Converts string to integer.", "toLower": "Converts string to lowercase.", "replace": "Replaces substring. Usage: {{value | replace:'find':'replace'}}", "truncate": "Truncates string. Usage: {{value | truncate:maxLength}}", "or": "Fallback. Usage: {{primaryValue | or:fallbackValue}}", "stripAgentPrefix": "Removes '@' and suffix like '-agent'.", "extractShortPurpose": "Extracts brief purpose from English summary.", "extractEnglishResponseTranslation": "Conceptual: If summaryText_en contains 'User response (original lang): ... English interpretation/summary of response: ...', this extracts the English interpretation.", "extractEnglishReasoningLinkOrSummary": "Conceptual: Extracts a link to an English reasoning document or a direct English summary of reasoning, if present in summaryText_en.", "extractFocusFromReportSummary": "Conceptual: Extracts the main focus/topic of a report from its summary.", "translateToEnglishIfDifferent": "Conceptual: Takes a value and a language code. If language code is not 'en-US' (or similar), translates value to English. Otherwise, returns original value. This is a powerful placeholder for Scribe's/UO's LLM capabilities."}}