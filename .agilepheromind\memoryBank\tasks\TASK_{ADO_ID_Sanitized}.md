---
id_ado: "Azure#67890"
title_en: "Implement backend API endpoint for 2FA validation"
title_originalLang: "Implémenter le point d'API backend pour la validation 2FA"
parent_us_id_ado: "Azure#12345"
status_pheromind_en: "DevelopmentInProgress"
status_ado_en: "Active"
assignee_pheromind_id_en: "a1b2c3d4-e5f6-7890-1234-567890abcdef" # Pheromind User ID
assignee_ado_displayName: "KRISTOU.OMNILOG, Abdessalam" # Last known ADO assignee
estimatedHours_en: 12
actualHours_en: 0
creationDate_pheromind: "YYYY-MM-DDTHH:MM:SSZ"
lastModifiedDate_pheromind: "YYYY-MM-DDTHH:MM:SSZ"
tags_en: ["backend", "api", "security", "dotnet"]
reasoningChainLinks_en:
  unitTestGeneration: "03_SPECS/Test_Scenarios/unit_tests_scenarios_2FAMethod_....md"
relatedCommits: # List of commit hashes related to this task
  - "abcdef1234567890"
testCasesGenerated_en: # Links to generated test artifacts
  - path: "tests/ProjectName.Api.Tests/TwoFactorAuthServiceTests.cs"
    scenarioReportPath_localized: "03_SPECS/Test_Scenarios/unit_tests_scenarios_2FAMethod_....fr.md"
    description_en: "Unit test skeletons for TwoFactorAuthService."
relatedDocumentation_localized: []
lastVerificationFailure_en: null # Link to a pre-commit check failure report if any
---

## Description (English)
Create a new .NET Core API endpoint `/api/auth/validate-2fa` that accepts a user identifier and a 2FA code.
It should validate the code against the user's registered 2FA method.

## Technical Notes & Implementation Details (English)
- Use ASP.NET Core Identity for 2FA provider management.
- Endpoint should be secured with `[Authorize]`.
- Return standard HTTP status codes.

## Pheromind Status History (English)
- `YYYY-MM-DDTHH:MM:SSZ`: `ToDo` - `@task-breakdown-estimator` - "Task created."
- `YYYY-MM-DDTHH:MM:SSZ`: `DevelopmentInProgress` - `@developer-agent` - "Branch created, starting implementation."

## Developer Notes (English)
# Specific notes for this task
- Remember to add logging for successful and failed 2FA attempts.

## Blockers (English)
# - Description: Date_Identified - Status (Active/Resolved)
- "Waiting for final decision on 2FA code expiration time. - YYYY-MM-DD - Active"

## Affected Files (Relative paths from repo root)
# List of files primarily touched by this task
- `src/ProjectName.Api/Controllers/AuthController.cs`
- `src/ProjectName.Application/Auth/TwoFactorAuthService.cs`