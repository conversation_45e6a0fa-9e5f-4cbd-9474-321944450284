workflow:
  name: "Technical Debt Analysis"
  description: >
    Analyse la dette technique et les code smells d’un projet ou module : collecte du code, analyse statique/heuristique, priorisation, suggestions, génération et enregistrement du rapport.

  steps:
    - id: "define_scope"
      action: "Définir la cible et collecter le code"
      agent: "code-reviewer-assistant"
      inputs:
        scope: "{{scope}}"
      outputs:
        code: "{{code}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "analyze_code"
      action: "Analyser le code et détecter la dette technique"
      agent: "code-reviewer-assistant"
      inputs:
        code: "{{code}}"
      outputs:
        debt_items: "{{debt_items}}"
    - id: "prioritize_suggestions"
      action: "Prioriser et suggérer les refactorings"
      agent: "code-reviewer-assistant"
      inputs:
        debt_items: "{{debt_items}}"
      outputs:
        suggestions: "{{suggestions}}"
    - id: "generate_report"
      action: "Générer et traduire le rapport de dette technique"
      agent: "code-reviewer-assistant"
      inputs:
        suggestions: "{{suggestions}}"
        user_language: "{{user_language}}"
      outputs:
        report: "{{report}}"
    - id: "record_report"
      action: "Enregistrer le rapport dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        report: "{{report}}"

  transitions: []
