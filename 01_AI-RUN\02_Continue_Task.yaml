workflow:
  name: "Continue Development Task"
  description: >
    Permet à un développeur de reprendre efficacement une tâche technique : chargement du contexte, préparation Git, assistance contextuelle, gestion des erreurs et clarification.

  steps:
    - id: "check_onboarding"
      action: "Vérifier que l'onboarding est complet"
      agent: "uber-orchestrator"
      on_fail:
        next_workflow: "00_System_Bootstrap"
    - id: "fetch_task"
      action: "Récupérer le contexte de la tâche Azure DevOps"
      agent: "devops-connector"
      inputs:
        task_id: "{{task_id}}"
      outputs:
        task_details: "{{task_details}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "update_context"
      action: "Mettre à jour le contexte interne et traduire en anglais"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        task_details: "{{task_details}}"
      outputs:
        context: "{{context}}"
    - id: "clarify_task"
      action: "Vérifier la clarté du contexte de la tâche"
      agent: "uber-orchestrator"
      inputs:
        context: "{{context}}"
      condition: "is_ambiguous(context)"
      on_true:
        sub_workflow: "XX_Handle_Clarification_Response"
        wait_for_response: true
    - id: "prepare_env"
      action: "Préparer l’environnement de développement (branche Git, fichiers)"
      agent: "developer-agent"
      inputs:
        context: "{{context}}"
      outputs:
        dev_env: "{{dev_env}}"
      on_fail:
        notify_user: true
    - id: "assist_implementation"
      action: "Assister le développeur pendant l’implémentation"
      agent: "developer-agent"
      inputs:
        dev_env: "{{dev_env}}"
        context: "{{context}}"
      outputs:
        session_notes: "{{session_notes}}"
      on_fail:
        sub_workflow: "XX_Handle_Clarification_Response"
    - id: "record_progress"
      action: "Enregistrer l’avancement et les décisions"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        session_notes: "{{session_notes}}"

  transitions: []
