workflow:
  name: "Project Environment Setup"
  description: >
    Guide l’utilisateur pour cloner un repo ou initialiser un projet .NET/Angular, configurer ADO, générer Docker/pipeline, commit et enregistrer la configuration.

  steps:
    - id: "choose_setup"
      action: "Demander à l’utilisateur s’il veut cloner un repo ou init un projet"
      agent: "project-setup-agent"
      outputs:
        setup_choice: "{{setup_choice}}"
    - id: "clone_or_init"
      action: "<PERSON><PERSON><PERSON> le repo ou initialiser les projets"
      agent: "project-setup-agent"
      inputs:
        setup_choice: "{{setup_choice}}"
      outputs:
        project_paths: "{{project_paths}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "configure_ado"
      action: "Configurer la connexion Azure DevOps"
      agent: "devops-connector"
      inputs:
        project_paths: "{{project_paths}}"
      outputs:
        ado_config: "{{ado_config}}"
      on_fail:
        notify_user: true
        stop: true
    - id: "generate_docker_pipeline"
      action: "Générer les Dockerfiles et pipeline"
      agent: "project-setup-agent"
      inputs:
        project_paths: "{{project_paths}}"
      outputs:
        config_files: "{{config_files}}"
    - id: "git_commit"
      action: "Commit initial du projet"
      agent: "project-setup-agent"
      inputs:
        project_paths: "{{project_paths}}"
      outputs:
        commit_status: "{{commit_status}}"
    - id: "record_setup"
      action: "Enregistrer la configuration dans la base"
      agent: "orchestrator-pheromone-scribe"
      inputs:
        project_paths: "{{project_paths}}"
        ado_config: "{{ado_config}}"
        config_files: "{{config_files}}"
        commit_status: "{{commit_status}}"

  transitions: []
