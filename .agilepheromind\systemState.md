# AgilePheromind: System State
# This file is managed by AgilePheromind.
# Location: .agilepheromind/systemState.md
# This file IS VERSIONED by Git.

## System Information
- **systemVersion:** `AgilePheromind-1.2.0` # Updated based on releases
- **lastUpdated:** `{{TIMESTAMP}}` # Timestamp of the last Scribe write to any Pheromind file
- **onboardingComplete:** true # Global flag indicating if initial user & project setup was ever done

## Active Workflow
- **scriptPath:** `01_AI-RUN/00_System_Bootstrap.md`
- **currentPhase_en:** `Completion`
- **status_en:** `complete` # e.g., "pending_clarification", "in_progress", "error", "complete"
- **startTime:** `{{TIMESTAMP}}`
- **lastError_en:** null # Object: { agent, phase_en, message_en, timestamp } or null
- **retryAttempts:** 0
- **pausedContext:** null # Object: { scriptPath, phaseName, originalAgent, originalTaskParams_en } - Used when a workflow pauses for clarification
- **history:** # Array of recent workflow steps/handoffs (brief summary)
    - `- Step 1: ...`
    - `- Step 2: ...`

## Clarification Context
- **pendingClarificationId:** null # UUID of the current pending clarification
- **originalAgent:** null # Agent that requested clarification (e.g., "@po-assistant")
- **originalPromptContext_en:** null # Brief English context of what was being done
- **clarificationQuestion_en:** null # The English question posed (before translation to user)
- **timestampRequested:** null

## Documentation Registry
# Links to key documents generated by or used by AgilePheromind.
# Format: - "Logical Name (English)": "Relative Path from project root"
# Paths here are primarily to localized documents if they are user-facing reports,
# or to English documents if they are internal system docs or templates.
# Scribe maintains this list based on agent outputs.
- **"Project Coding Conventions (English)":** `02_AI-DOCS/Conventions/coding_conventions.md`
- **"Project Design Conventions (English)":** `02_AI-DOCS/Conventions/design_conventions.md`
- **"Project Architecture Overview (English)":** `02_AI-DOCS/Architecture/project_architecture_overview.md`
- **"Bootstrap Completion Report (Example User Language)":** `02_AI-DOCS/System_Reports/bootstrap_completion_report_2025-05-19_fr.md`
# Add more entries as documents are created/managed by workflows...

## System Health
- **lastCheck:** `{{TIMESTAMP}}`
- **status_en:** `UNKNOWN` # e.g., "OK", "WARNING", "ERROR"
- **mcpStatus:** # Map of MCP name to its status { name: "Azure DevOps MCP", status: "OK", lastChecked: "{{TIMESTAMP}}" }
    - `AzureDevOps_MCP`: `{ status: "OK", lastChecked: "..." }`
    - `GitTools_MCP`: `{ status: "DEGRADED", message: "...", lastChecked: "..." }`
- **errorCountToday:** 0 # Resettable counter for workflow errors
- **lastPheromoneWriteSuccess:** true # Status of the last Scribe write operation to any .md file

## Notifications (Localized for User)
# List of recent important notifications for the user.
# UO translates messages to currentUser.lastInteractionLanguage before adding.
# Format: - "[{{TIMESTAMP}}] {{Message_Localized}}"
# This section might be cleared periodically or by user action.
- `[2025-05-21T10:00:00Z] Le rapport d'analyse de la dette technique est prêt.`
- `[2025-05-20T15:30:00Z] Une clarification est nécessaire pour la User Story Azure#12345.`

## Available Workflows
# This list is maintained by the '18_Maintain_Available_Workflows.md' script.
# Used by @head-orchestrator to provide help and suggestions. All fields are in English.
# - id: "internal_workflow_id"
#   name_en: "User-Friendly Name of Workflow"
#   description_en: "Brief objective of the workflow."
#   scriptPath: "01_AI-RUN/script_file_name.md"
#   typicalUserRoles_en: ["Role1", "Role2"] # e.g., "Developer", "ProductOwner"
#   exampleCommand_template_en: "AgilePheromind command_verb {{param1}} with {{param2}}"
#   category_en: "Category Name" # New field for better help filtering, e.g., "User Story Management", "Code Analysis", "Project Setup"
#   keywords_en: ["keyword1", "keyword2", "user story", "task"] # New field for semantic search by HO

- id: "bootstrap_system"
  name_en: "System Bootstrap and Initial Configuration"
  description_en: "Initializes or verifies AgilePheromind's core setup, user, and project information. Run if onboarding is incomplete."
  scriptPath: "01_AI-RUN/00_System_Bootstrap.md"
  typicalUserRoles_en: ["SystemAdmin", "TechLead"]
  exampleCommand_template_en: "AgilePheromind run onboarding setup"
  category_en: "System Administration"
  keywords_en: ["setup", "onboarding", "configuration", "init", "bootstrap"]
- id: "start_us"
  name_en: "Start a User Story"
  description_en: "Initiates work on a specific Azure DevOps User Story, including task breakdown and Git branch setup."
  scriptPath: "01_AI-RUN/01_Start_User_Story.md"
  typicalUserRoles_en: ["Developer"]
  exampleCommand_template_en: "AgilePheromind start US Azure#{{US_ID}}"
  category_en: "User Story Management"
  keywords_en: ["user story", "task breakdown", "git branch", "start work", "US"]
# ... (other workflow definitions from your original pheromone.json, augmented with category_en and keywords_en)
- id: "provide_help" # New workflow for I.3
  name_en: "Provide Help and List Workflows"
  description_en: "Lists available AgilePheromind workflows or provides help on a specific topic/workflow."
  scriptPath: "01_AI-RUN/XX_Provide_Help.md" # To be created
  typicalUserRoles_en: ["User"]
  exampleCommand_template_en: "AgilePheromind help with {{topic_or_workflow_name}}"
  category_en: "System Interaction"
  keywords_en: ["help", "command list", "workflow list", "assist", "documentation"]

---